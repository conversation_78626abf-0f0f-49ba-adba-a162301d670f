/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-bottom: 1px solid #eee;
}

.logo-section {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.logo {
    height: 50px;
    width: auto;
}

.tagline {
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.social-stats {
    display: flex;
    align-items: center;
    gap: 15px;
    font-size: 12px;
    color: #888;
}

.social-icons {
    display: flex;
    gap: 8px;
}

.social-icon {
    color: #666;
    font-size: 16px;
    text-decoration: none;
    transition: color 0.3s;
}

.social-icon:hover {
    color: #ff4444;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 24px;
    color: #333;
    cursor: pointer;
}

.subscribe-btn {
    background: #ff4444;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.3s;
    display: flex;
    align-items: center;
    gap: 8px;
}

.subscribe-btn:hover {
    background: #e63939;
}

/* Navigation Styles */
.main-nav {
    padding: 15px 0;
}

.nav-list {
    display: flex;
    list-style: none;
    gap: 30px;
    margin-bottom: 15px;
}

.nav-categories {
    display: flex;
    list-style: none;
    gap: 25px;
    flex-wrap: wrap;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    padding: 8px 0;
    transition: color 0.3s;
    display: flex;
    align-items: center;
    gap: 6px;
}

.nav-link:hover,
.nav-link.active {
    color: #ff4444;
}

/* Main Content Styles */
.main-content {
    padding: 30px 0;
}

.content-grid {
    display: grid;
    gap: 30px;
}

/* Featured Article */
.featured-article {
    margin-bottom: 40px;
}

.article-card {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s, box-shadow 0.3s;
}

.article-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 25px rgba(0,0,0,0.15);
}

.article-card.featured {
    display: grid;
    grid-template-columns: 1fr;
}

.article-image {
    width: 100%;
    height: 380px;
    object-fit: cover;
}

.article-content {
    padding: 25px;
}

.article-title {
    font-size: 24px;
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 15px;
    color: #333;
}

.featured .article-title {
    font-size: 32px;
}

.article-meta {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    font-size: 14px;
    color: #666;
}

.category {
    color: #ff4444;
    font-weight: 500;
}

.article-excerpt {
    color: #666;
    line-height: 1.6;
}

/* Articles Grid */
.articles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 25px;
}

.articles-grid .article-image {
    height: 200px;
}

.articles-grid .article-title {
    font-size: 18px;
}

/* Sidebar */
.sidebar {
    background: white;
    margin-top: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 15px rgba(0,0,0,0.1);
}

.sidebar-section {
    padding: 25px;
    border-bottom: 1px solid #eee;
}

.sidebar-section:last-child {
    border-bottom: none;
}

.sidebar-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333;
}

.trending-item,
.resource-item {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #f0f0f0;
}

.trending-item:last-child,
.resource-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.trending-image,
.resource-image {
    width: 72px;
    height: 72px;
    border-radius: 6px;
    object-fit: cover;
}

.trending-title,
.resource-title {
    font-size: 14px;
    font-weight: 500;
    line-height: 1.4;
    color: #333;
}

/* Footer */
.footer {
    background: #333;
    color: white;
    margin-top: 50px;
    padding: 40px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}

.footer-section h4 {
    margin-bottom: 15px;
    font-weight: 600;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 8px;
}

.footer-section a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s;
}

.footer-section a:hover {
    color: #ff4444;
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-link {
    color: #ccc;
    font-size: 20px;
    transition: color 0.3s;
}

.social-link:hover {
    color: #ff4444;
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #555;
    color: #ccc;
    font-size: 14px;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 15% auto;
    padding: 30px;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    position: relative;
}

.close {
    position: absolute;
    right: 20px;
    top: 15px;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: #aaa;
}

.close:hover {
    color: #333;
}

.newsletter-form {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.newsletter-form input {
    flex: 1;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 16px;
}

.newsletter-form button {
    background: #ff4444;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
}

/* Sponsored Content */
.sponsored-section {
    margin-top: 40px;
    padding: 30px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 15px rgba(0,0,0,0.1);
}

.sponsored-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
}

.sponsored-header h3 {
    font-size: 24px;
    font-weight: 600;
    color: #333;
}

.sponsored-label {
    background: #ff4444;
    color: white;
    padding: 4px 12px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.sponsored-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
}

.sponsored-card {
    background: #f8f9fa;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.3s;
}

.sponsored-card:hover {
    transform: translateY(-3px);
}

.sponsored-image {
    width: 100%;
    height: 190px;
    object-fit: cover;
}

.sponsored-content {
    padding: 20px;
}

.sponsored-content h4 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
}

.sponsored-content p {
    font-size: 14px;
    color: #666;
    line-height: 1.5;
}

/* Expert Insights */
.insights-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.insight-item {
    padding: 15px 0;
    border-bottom: 1px solid #f0f0f0;
}

.insight-item:last-child {
    border-bottom: none;
}

.insight-title {
    font-size: 14px;
    font-weight: 500;
    line-height: 1.4;
    color: #333;
    margin-bottom: 5px;
}

.insight-date {
    font-size: 12px;
    color: #888;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.article-card {
    animation: fadeInUp 0.6s ease-out;
}

.sidebar {
    animation: slideInRight 0.6s ease-out;
}

/* Loading States */
.loading {
    opacity: 0.7;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #ff4444;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .container {
        padding: 0 15px;
    }

    .articles-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }
}

@media (max-width: 768px) {
    .header-top {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .logo-section {
        align-items: center;
    }

    .menu-toggle {
        display: block;
        position: absolute;
        top: 20px;
        right: 20px;
    }

    .main-nav {
        display: none;
        background: white;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-top: 15px;
        border-radius: 8px;
        padding: 20px;
    }

    .main-nav.active {
        display: block;
        animation: fadeInUp 0.3s ease-out;
    }

    .nav-list,
    .nav-categories {
        flex-direction: column;
        gap: 10px;
    }

    .nav-link {
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;
    }

    .nav-link:last-child {
        border-bottom: none;
    }

    .articles-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .featured .article-title {
        font-size: 24px;
    }

    .article-content {
        padding: 20px;
    }

    .newsletter-form {
        flex-direction: column;
    }

    .sponsored-grid {
        grid-template-columns: 1fr;
    }

    .sponsored-section {
        margin-top: 20px;
        padding: 20px;
    }

    .sidebar {
        margin-top: 20px;
    }

    .footer-content {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 10px;
    }

    .header-top {
        padding: 15px 0;
    }

    .logo {
        height: 40px;
    }

    .subscribe-btn {
        padding: 10px 15px;
        font-size: 14px;
    }

    .featured .article-title {
        font-size: 20px;
    }

    .article-title {
        font-size: 16px;
    }

    .article-content {
        padding: 15px;
    }

    .sponsored-section {
        padding: 15px;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .social-stats {
        flex-direction: column;
        gap: 10px;
    }
}

/* Print Styles */
@media print {
    .header,
    .sidebar,
    .footer,
    .sponsored-section {
        display: none;
    }

    .main-content {
        padding: 0;
    }

    .article-card {
        box-shadow: none;
        border: 1px solid #ddd;
        margin-bottom: 20px;
        page-break-inside: avoid;
    }
}
