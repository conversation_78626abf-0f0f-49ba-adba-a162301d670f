// DOM Elements
const menuToggle = document.getElementById('menuToggle');
const mainNav = document.getElementById('mainNav');
const subscribeBtn = document.getElementById('subscribeBtn');
const newsletterModal = document.getElementById('newsletterModal');
const closeModal = document.getElementById('closeModal');

// Mobile Menu Toggle
menuToggle.addEventListener('click', () => {
    mainNav.classList.toggle('active');
    
    // Toggle hamburger icon
    const icon = menuToggle.querySelector('i');
    if (mainNav.classList.contains('active')) {
        icon.classList.remove('fa-bars');
        icon.classList.add('fa-times');
    } else {
        icon.classList.remove('fa-times');
        icon.classList.add('fa-bars');
    }
});

// Newsletter Modal
subscribeBtn.addEventListener('click', () => {
    newsletterModal.style.display = 'block';
});

closeModal.addEventListener('click', () => {
    newsletterModal.style.display = 'none';
});

// Close modal when clicking outside
window.addEventListener('click', (e) => {
    if (e.target === newsletterModal) {
        newsletterModal.style.display = 'none';
    }
});

// Newsletter Form Submission
const newsletterForm = document.querySelector('.newsletter-form');
newsletterForm.addEventListener('submit', (e) => {
    e.preventDefault();
    const email = e.target.querySelector('input[type="email"]').value;
    
    // Simulate subscription
    alert(`Thank you for subscribing with email: ${email}`);
    newsletterModal.style.display = 'none';
    e.target.reset();
});

// Smooth Scrolling for Navigation Links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Article Card Hover Effects
document.querySelectorAll('.article-card').forEach(card => {
    card.addEventListener('mouseenter', () => {
        card.style.transform = 'translateY(-5px)';
    });
    
    card.addEventListener('mouseleave', () => {
        card.style.transform = 'translateY(0)';
    });
});

// Lazy Loading for Images
const images = document.querySelectorAll('img');
const imageObserver = new IntersectionObserver((entries, observer) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            const img = entry.target;
            img.src = img.src; // Trigger loading
            img.classList.add('loaded');
            observer.unobserve(img);
        }
    });
});

images.forEach(img => {
    imageObserver.observe(img);
});

// Search Functionality (Basic)
function createSearchBox() {
    const searchContainer = document.createElement('div');
    searchContainer.className = 'search-container';
    searchContainer.innerHTML = `
        <input type="text" placeholder="Search articles..." class="search-input" id="searchInput">
        <button class="search-btn" id="searchBtn">
            <i class="fas fa-search"></i>
        </button>
    `;
    
    // Add search to header
    const headerActions = document.querySelector('.header-actions');
    headerActions.insertBefore(searchContainer, headerActions.firstChild);
    
    // Search functionality
    const searchInput = document.getElementById('searchInput');
    const searchBtn = document.getElementById('searchBtn');
    
    function performSearch() {
        const query = searchInput.value.toLowerCase().trim();
        if (query) {
            // Simple search simulation
            alert(`Searching for: "${query}"`);
            // In a real implementation, this would filter articles or redirect to search results
        }
    }
    
    searchBtn.addEventListener('click', performSearch);
    searchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            performSearch();
        }
    });
}

// Dynamic Content Loading
function loadMoreArticles() {
    const articlesGrid = document.querySelector('.articles-grid');
    const loadMoreBtn = document.createElement('button');
    loadMoreBtn.className = 'load-more-btn';
    loadMoreBtn.textContent = 'Load More Articles';
    loadMoreBtn.style.cssText = `
        background: #ff4444;
        color: white;
        border: none;
        padding: 15px 30px;
        border-radius: 6px;
        font-weight: 500;
        cursor: pointer;
        margin: 30px auto;
        display: block;
        transition: background 0.3s;
    `;
    
    loadMoreBtn.addEventListener('mouseenter', () => {
        loadMoreBtn.style.background = '#e63939';
    });
    
    loadMoreBtn.addEventListener('mouseleave', () => {
        loadMoreBtn.style.background = '#ff4444';
    });
    
    loadMoreBtn.addEventListener('click', () => {
        // Simulate loading more articles
        const newArticles = [
            {
                title: "New Ransomware Strain Targets Government Agencies",
                category: "Malware / Government",
                date: "Jul 25, 2025",
                excerpt: "A sophisticated ransomware operation has been targeting government agencies across multiple countries...",
                image: "https://via.placeholder.com/300x200/777777/ffffff?text=Ransomware"
            },
            {
                title: "IoT Botnet Compromises Smart Home Devices",
                category: "IoT Security / Smart Home",
                date: "Jul 25, 2025",
                excerpt: "Security researchers have discovered a massive botnet targeting smart home devices worldwide...",
                image: "https://via.placeholder.com/300x200/888888/ffffff?text=IoT+Security"
            }
        ];
        
        newArticles.forEach(article => {
            const articleElement = document.createElement('article');
            articleElement.className = 'article-card';
            articleElement.innerHTML = `
                <img src="${article.image}" alt="Article" class="article-image">
                <div class="article-content">
                    <h2 class="article-title">${article.title}</h2>
                    <div class="article-meta">
                        <span class="date">${article.date}</span>
                        <span class="category">${article.category}</span>
                    </div>
                    <p class="article-excerpt">${article.excerpt}</p>
                </div>
            `;
            articlesGrid.appendChild(articleElement);
        });
        
        // Remove load more button after loading
        loadMoreBtn.remove();
    });
    
    // Add load more button after articles grid
    articlesGrid.parentNode.insertBefore(loadMoreBtn, articlesGrid.nextSibling);
}

// Social Media Share Functionality
function addShareButtons() {
    document.querySelectorAll('.article-card').forEach(card => {
        const shareContainer = document.createElement('div');
        shareContainer.className = 'share-buttons';
        shareContainer.innerHTML = `
            <button class="share-btn" data-platform="twitter">
                <i class="fab fa-twitter"></i>
            </button>
            <button class="share-btn" data-platform="facebook">
                <i class="fab fa-facebook"></i>
            </button>
            <button class="share-btn" data-platform="linkedin">
                <i class="fab fa-linkedin"></i>
            </button>
        `;
        
        shareContainer.style.cssText = `
            position: absolute;
            top: 15px;
            right: 15px;
            display: flex;
            gap: 8px;
            opacity: 0;
            transition: opacity 0.3s;
        `;
        
        card.style.position = 'relative';
        card.appendChild(shareContainer);
        
        // Show share buttons on hover
        card.addEventListener('mouseenter', () => {
            shareContainer.style.opacity = '1';
        });
        
        card.addEventListener('mouseleave', () => {
            shareContainer.style.opacity = '0';
        });
        
        // Share functionality
        shareContainer.addEventListener('click', (e) => {
            if (e.target.closest('.share-btn')) {
                const platform = e.target.closest('.share-btn').dataset.platform;
                const title = card.querySelector('.article-title').textContent;
                const url = window.location.href;
                
                let shareUrl = '';
                switch (platform) {
                    case 'twitter':
                        shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(title)}&url=${encodeURIComponent(url)}`;
                        break;
                    case 'facebook':
                        shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
                        break;
                    case 'linkedin':
                        shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`;
                        break;
                }
                
                if (shareUrl) {
                    window.open(shareUrl, '_blank', 'width=600,height=400');
                }
            }
        });
    });
}

// Initialize all features when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    createSearchBox();
    loadMoreArticles();
    addShareButtons();
    
    // Add CSS for search box
    const searchStyles = document.createElement('style');
    searchStyles.textContent = `
        .search-container {
            display: flex;
            align-items: center;
            gap: 5px;
            margin-right: 15px;
        }
        
        .search-input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            width: 200px;
        }
        
        .search-btn {
            background: #ff4444;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .search-btn:hover {
            background: #e63939;
        }
        
        .share-btn {
            background: rgba(255, 255, 255, 0.9);
            border: none;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.3s;
        }
        
        .share-btn:hover {
            background: #ff4444;
            color: white;
        }
        
        @media (max-width: 768px) {
            .search-container {
                order: -1;
                margin-bottom: 15px;
                margin-right: 0;
            }
            
            .search-input {
                width: 100%;
            }
        }
    `;
    document.head.appendChild(searchStyles);
});

// Performance optimization: Debounce scroll events
let ticking = false;

function updateScrollPosition() {
    // Add scroll-based animations or effects here
    ticking = false;
}

window.addEventListener('scroll', () => {
    if (!ticking) {
        requestAnimationFrame(updateScrollPosition);
        ticking = true;
    }
});

// Error handling for images
document.querySelectorAll('img').forEach(img => {
    img.addEventListener('error', () => {
        img.src = 'https://via.placeholder.com/300x200/cccccc/666666?text=Image+Not+Found';
    });
});

// Dark mode toggle (bonus feature)
function addDarkModeToggle() {
    const darkModeToggle = document.createElement('button');
    darkModeToggle.className = 'dark-mode-toggle';
    darkModeToggle.innerHTML = '<i class="fas fa-moon"></i>';
    darkModeToggle.title = 'Toggle Dark Mode';

    // Add to header actions
    const headerActions = document.querySelector('.header-actions');
    headerActions.insertBefore(darkModeToggle, headerActions.firstChild);

    // Check for saved theme preference
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark') {
        document.body.classList.add('dark-mode');
        darkModeToggle.innerHTML = '<i class="fas fa-sun"></i>';
    }

    darkModeToggle.addEventListener('click', () => {
        document.body.classList.toggle('dark-mode');
        const isDark = document.body.classList.contains('dark-mode');

        darkModeToggle.innerHTML = isDark ? '<i class="fas fa-sun"></i>' : '<i class="fas fa-moon"></i>';
        localStorage.setItem('theme', isDark ? 'dark' : 'light');
    });
}

// Reading progress indicator
function addReadingProgress() {
    const progressBar = document.createElement('div');
    progressBar.className = 'reading-progress';
    document.body.appendChild(progressBar);

    window.addEventListener('scroll', () => {
        const winScroll = document.body.scrollTop || document.documentElement.scrollTop;
        const height = document.documentElement.scrollHeight - document.documentElement.clientHeight;
        const scrolled = (winScroll / height) * 100;
        progressBar.style.width = scrolled + '%';
    });
}

// Back to top button
function addBackToTop() {
    const backToTop = document.createElement('button');
    backToTop.className = 'back-to-top';
    backToTop.innerHTML = '<i class="fas fa-arrow-up"></i>';
    backToTop.title = 'Back to Top';
    document.body.appendChild(backToTop);

    window.addEventListener('scroll', () => {
        if (window.pageYOffset > 300) {
            backToTop.classList.add('visible');
        } else {
            backToTop.classList.remove('visible');
        }
    });

    backToTop.addEventListener('click', () => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
}

// Cookie consent banner
function addCookieConsent() {
    const cookieBanner = document.createElement('div');
    cookieBanner.className = 'cookie-banner';
    cookieBanner.innerHTML = `
        <div class="cookie-content">
            <p>We use cookies to enhance your browsing experience and analyze our traffic. By continuing to use this site, you consent to our use of cookies.</p>
            <div class="cookie-actions">
                <button class="cookie-accept">Accept</button>
                <button class="cookie-decline">Decline</button>
            </div>
        </div>
    `;

    // Check if user has already made a choice
    if (!localStorage.getItem('cookieConsent')) {
        document.body.appendChild(cookieBanner);

        cookieBanner.querySelector('.cookie-accept').addEventListener('click', () => {
            localStorage.setItem('cookieConsent', 'accepted');
            cookieBanner.remove();
        });

        cookieBanner.querySelector('.cookie-decline').addEventListener('click', () => {
            localStorage.setItem('cookieConsent', 'declined');
            cookieBanner.remove();
        });
    }
}

// Initialize enhanced features
document.addEventListener('DOMContentLoaded', () => {
    // Previous initialization code...
    createSearchBox();
    loadMoreArticles();
    addShareButtons();

    // New enhanced features
    addDarkModeToggle();
    addReadingProgress();
    addBackToTop();
    addCookieConsent();

    // Enhanced CSS for new features
    const enhancedStyles = document.createElement('style');
    enhancedStyles.textContent = `
        /* Dark Mode Styles */
        .dark-mode-toggle {
            background: none;
            border: 1px solid #ddd;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            margin-right: 15px;
            transition: all 0.3s;
        }

        .dark-mode-toggle:hover {
            background: #f0f0f0;
        }

        .dark-mode {
            background: #1a1a1a;
            color: #e0e0e0;
        }

        .dark-mode .header,
        .dark-mode .article-card,
        .dark-mode .sidebar,
        .dark-mode .sponsored-section {
            background: #2d2d2d;
            color: #e0e0e0;
        }

        .dark-mode .article-title,
        .dark-mode .sidebar-title {
            color: #ffffff;
        }

        /* Reading Progress */
        .reading-progress {
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 3px;
            background: #ff4444;
            z-index: 9999;
            transition: width 0.3s;
        }

        /* Back to Top */
        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 50px;
            height: 50px;
            background: #ff4444;
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s;
            z-index: 1000;
        }

        .back-to-top.visible {
            opacity: 1;
            visibility: visible;
        }

        .back-to-top:hover {
            background: #e63939;
            transform: translateY(-2px);
        }

        /* Cookie Banner */
        .cookie-banner {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: #333;
            color: white;
            padding: 20px;
            z-index: 10000;
            animation: slideUp 0.3s ease-out;
        }

        @keyframes slideUp {
            from { transform: translateY(100%); }
            to { transform: translateY(0); }
        }

        .cookie-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 20px;
        }

        .cookie-actions {
            display: flex;
            gap: 10px;
        }

        .cookie-accept,
        .cookie-decline {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
        }

        .cookie-accept {
            background: #ff4444;
            color: white;
        }

        .cookie-decline {
            background: transparent;
            color: white;
            border: 1px solid white;
        }

        @media (max-width: 768px) {
            .cookie-content {
                flex-direction: column;
                text-align: center;
            }

            .back-to-top {
                bottom: 20px;
                right: 20px;
                width: 45px;
                height: 45px;
            }
        }
    `;
    document.head.appendChild(enhancedStyles);
});
